#!/usr/bin/env python3
"""
Test script to verify immediate callback acknowledgment and pre-handler monitoring.
Tests the critical fixes for callback query expiration issues.
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_immediate_callback_acknowledgment():
    """Test immediate callback acknowledgment before validation"""
    print("⚡ Testing Immediate Callback Acknowledgment")
    print("-" * 50)
    
    try:
        from bots.management_bot import handle_callback_query
        
        # Create mock callback with realistic timing
        mock_call = Mock()
        mock_call.id = "immediate_test_123"
        mock_call.data = "mgmt_analytics"
        mock_call.from_user = Mock()
        mock_call.from_user.id = 7729984017
        mock_call.message = Mock()
        mock_call.message.chat = Mock()
        mock_call.message.chat.id = 12345
        mock_call.message.date = time.time() - 2  # 2 seconds old
        
        # Mock the management bot to track acknowledgment
        ack_called = False
        ack_time = None
        
        def mock_answer_callback_query(callback_id, text, show_alert=False):
            nonlocal ack_called, ack_time
            ack_called = True
            ack_time = time.time()
            return True
        
        # Test immediate acknowledgment
        start_time = time.time()
        
        with patch('bots.management_bot.management_bot') as mock_bot:
            mock_bot.answer_callback_query = mock_answer_callback_query
            mock_bot.send_message = Mock()
            
            with patch('bots.management_bot.is_admin') as mock_admin:
                mock_admin.return_value = True
                
                # This should immediately acknowledge the callback
                handle_callback_query(mock_call)
                
                acknowledgment_time = ack_time - start_time if ack_time else None
                
                if ack_called and acknowledgment_time is not None:
                    if acknowledgment_time < 0.1:  # Should be very fast
                        print(f"✅ Immediate acknowledgment: {acknowledgment_time:.3f}s")
                    else:
                        print(f"⚠️ Acknowledgment time: {acknowledgment_time:.3f}s (target: <0.1s)")
                else:
                    print("❌ Callback not acknowledged")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Immediate acknowledgment test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pre_handler_monitoring():
    """Test pre-handler monitoring and timing metrics"""
    print("\n📊 Testing Pre-Handler Monitoring")
    print("-" * 50)
    
    try:
        from bots.management_bot import (
            log_callback_timing_metrics,
            get_callback_timing_report,
            callback_timing_metrics,
            timing_metrics_lock
        )
        
        # Clear existing metrics
        with timing_metrics_lock:
            callback_timing_metrics.update({
                'total_callbacks_received': 0,
                'immediate_ack_times': [],
                'pre_handler_delays': [],
                'callback_ages_on_receipt': [],
                'max_pre_handler_delay': 0,
                'avg_immediate_ack_time': 0
            })
        
        # Log some test metrics
        log_callback_timing_metrics(0.002, 1.5, 2.0)  # Good performance
        log_callback_timing_metrics(0.005, 35.0, 38.0)  # Poor performance
        log_callback_timing_metrics(0.001, 0.8, 1.2)  # Excellent performance
        
        # Generate report
        report = get_callback_timing_report()
        
        if "CALLBACK TIMING PERFORMANCE REPORT" in report:
            print("✅ Timing report generated successfully")
            print("\nSample Report:")
            print(report)
        else:
            print("❌ Timing report generation failed")
            return False
        
        # Check if alerts are detected
        if "🚨 Callbacks arriving extremely old" in report:
            print("✅ Alert system detected old callbacks")
        else:
            print("⚠️ Alert system may not be working properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Pre-handler monitoring test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_polling_configuration():
    """Test optimized polling configuration"""
    print("\n🔧 Testing Polling Configuration")
    print("-" * 50)
    
    try:
        from src.bot_instance import POLLING_TIMEOUT
        import telebot.apihelper
        
        # Check polling timeout
        if POLLING_TIMEOUT <= 10:
            print(f"✅ Polling timeout optimized: {POLLING_TIMEOUT}s (target: ≤10s)")
        else:
            print(f"❌ Polling timeout too high: {POLLING_TIMEOUT}s (should be ≤10s)")
            return False
        
        # Check API timeouts
        if telebot.apihelper.CONNECT_TIMEOUT <= 5:
            print(f"✅ Connect timeout optimized: {telebot.apihelper.CONNECT_TIMEOUT}s")
        else:
            print(f"⚠️ Connect timeout: {telebot.apihelper.CONNECT_TIMEOUT}s (recommended: ≤5s)")
        
        if telebot.apihelper.READ_TIMEOUT <= 10:
            print(f"✅ Read timeout optimized: {telebot.apihelper.READ_TIMEOUT}s")
        else:
            print(f"⚠️ Read timeout: {telebot.apihelper.READ_TIMEOUT}s (recommended: ≤10s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Polling configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_age_validation():
    """Test relaxed callback age validation"""
    print("\n⏰ Testing Callback Age Validation")
    print("-" * 50)
    
    try:
        from bots.management_bot import handle_callback_query
        
        # Test with old but not extremely old callback (should be processed)
        mock_call = Mock()
        mock_call.id = "age_test_456"
        mock_call.data = "mgmt_main"
        mock_call.from_user = Mock()
        mock_call.from_user.id = 7729984017
        mock_call.message = Mock()
        mock_call.message.chat = Mock()
        mock_call.message.chat.id = 12345
        mock_call.message.date = time.time() - 45  # 45 seconds old (should be processed)
        
        processed = False
        
        with patch('bots.management_bot.management_bot') as mock_bot:
            mock_bot.answer_callback_query = Mock(return_value=True)
            mock_bot.send_message = Mock()
            
            with patch('bots.management_bot.is_admin') as mock_admin:
                mock_admin.return_value = True
                
                with patch('bots.management_bot.add_callback_to_queue') as mock_queue:
                    mock_queue.return_value = None
                    
                    handle_callback_query(mock_call)
                    
                    # Check if callback was queued (processed)
                    if mock_queue.called:
                        processed = True
                        print("✅ Old callback (45s) processed with relaxed validation")
                    else:
                        print("❌ Old callback (45s) rejected incorrectly")
        
        # Test with extremely old callback (should be rejected)
        mock_call.message.date = time.time() - 70  # 70 seconds old (should be rejected)
        
        with patch('bots.management_bot.management_bot') as mock_bot:
            mock_bot.answer_callback_query = Mock(return_value=True)
            mock_bot.send_message = Mock()
            
            with patch('bots.management_bot.is_admin') as mock_admin:
                mock_admin.return_value = True
                
                with patch('bots.management_bot.add_callback_to_queue') as mock_queue:
                    mock_queue.reset_mock()
                    
                    handle_callback_query(mock_call)
                    
                    # Check if callback was rejected
                    if not mock_queue.called:
                        print("✅ Extremely old callback (70s) correctly rejected")
                    else:
                        print("❌ Extremely old callback (70s) incorrectly processed")
                        return False
        
        return processed
        
    except Exception as e:
        print(f"❌ Callback age validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_integration():
    """Test overall system integration"""
    print("\n🔗 Testing System Integration")
    print("-" * 50)
    
    try:
        from bots.management_bot import (
            initialize_callback_system,
            callback_processor_running,
            get_callback_timing_report
        )
        
        # Test system initialization
        if callback_processor_running:
            print("✅ Callback processing system is running")
        else:
            print("❌ Callback processing system not running")
            return False
        
        # Test monitoring integration
        report = get_callback_timing_report()
        if report:
            print("✅ Monitoring system integrated")
        else:
            print("❌ Monitoring system not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ System integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_callback_acknowledgment_tests():
    """Run all callback acknowledgment and monitoring tests"""
    print("🚀 Immediate Callback Acknowledgment Tests")
    print("=" * 60)
    
    tests = [
        ("Immediate Callback Acknowledgment", test_immediate_callback_acknowledgment),
        ("Pre-Handler Monitoring", test_pre_handler_monitoring),
        ("Polling Configuration", test_polling_configuration),
        ("Callback Age Validation", test_callback_age_validation),
        ("System Integration", test_system_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All callback acknowledgment tests passed!")
        print("\n✨ Critical Fixes Implemented:")
        print("  • Immediate callback acknowledgment (<0.1s)")
        print("  • Optimized polling configuration (10s timeout)")
        print("  • Relaxed age validation (60s threshold)")
        print("  • Comprehensive pre-handler monitoring")
        print("  • Real-time performance diagnostics")
        print("  • Automatic polling performance monitoring")
        print("\n🎯 Expected Results:")
        print("  • All callbacks acknowledged within 1-2 seconds")
        print("  • No 'expired callback' warnings in normal operation")
        print("  • Consistent performance regardless of system load")
        print("  • Immediate user feedback for every button press")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = run_callback_acknowledgment_tests()
    sys.exit(0 if success else 1)

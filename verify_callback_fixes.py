#!/usr/bin/env python3
"""
Simple verification script for callback timeout fixes.
"""

import os
import re

def verify_callback_fixes():
    """Verify that callback timeout fixes have been implemented"""
    print("🔍 Verifying Callback Query Timeout Fixes")
    print("=" * 50)
    
    management_bot_path = "src/bots/management_bot.py"
    
    if not os.path.exists(management_bot_path):
        print("❌ Management bot file not found")
        return False
    
    with open(management_bot_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for required functions
    required_functions = [
        'safe_answer_callback_query',
        'validate_callback_query', 
        'handle_callback_error',
        'safe_callback_with_retry'
    ]
    
    print("📋 Checking for required functions:")
    for func in required_functions:
        if f"def {func}(" in content:
            print(f"  ✅ {func} - Found")
        else:
            print(f"  ❌ {func} - Missing")
            return False
    
    # Check for timeout constant
    if "CALLBACK_QUERY_TIMEOUT = 30" in content:
        print("  ✅ CALLBACK_QUERY_TIMEOUT constant - Found")
    else:
        print("  ❌ CALLBACK_QUERY_TIMEOUT constant - Missing")
        return False
    
    # Check for safe callback usage in critical functions
    print("\n🔧 Checking for safe callback usage:")
    
    critical_functions = [
        'handle_callback_query',
        'show_main_menu',
        'show_earnings_menu'
    ]
    
    for func in critical_functions:
        # Find the function definition
        func_pattern = rf"def {func}\(.*?\):(.*?)(?=def |\Z)"
        func_match = re.search(func_pattern, content, re.DOTALL)
        
        if func_match:
            func_content = func_match.group(1)
            if 'safe_answer_callback_query' in func_content:
                print(f"  ✅ {func} - Uses safe callback handling")
            else:
                print(f"  ⚠️ {func} - May still use direct callback handling")
        else:
            print(f"  ❌ {func} - Function not found")
    
    # Check for error handling improvements
    print("\n🛡️ Checking for error handling improvements:")
    
    if 'handle_callback_error' in content:
        print("  ✅ Centralized error handling - Found")
    else:
        print("  ❌ Centralized error handling - Missing")
        return False
    
    if 'fallback_message' in content:
        print("  ✅ Fallback message support - Found")
    else:
        print("  ❌ Fallback message support - Missing")
        return False
    
    # Count safe vs unsafe callback usage
    safe_callbacks = content.count('safe_answer_callback_query')
    direct_callbacks = content.count('management_bot.answer_callback_query')
    
    print(f"\n📊 Callback Usage Statistics:")
    print(f"  • Safe callback calls: {safe_callbacks}")
    print(f"  • Direct callback calls: {direct_callbacks}")
    
    if safe_callbacks > 0:
        print("  ✅ Safe callback handling is being used")
    else:
        print("  ❌ No safe callback handling found")
        return False
    
    # Check for timeout detection logic
    if 'time_diff > CALLBACK_QUERY_TIMEOUT' in content:
        print("  ✅ Timeout detection logic - Found")
    else:
        print("  ❌ Timeout detection logic - Missing")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All callback timeout fixes have been successfully implemented!")
    
    print("\n📋 Summary of Implemented Fixes:")
    print("  • Safe callback query answering with timeout detection")
    print("  • Callback query validation before processing")
    print("  • Fallback message support for expired queries")
    print("  • Centralized error handling with user-friendly messages")
    print("  • Retry logic for transient failures")
    print("  • Updated critical functions to use safe handling")
    
    print("\n✨ The management bot should now handle callback query timeouts gracefully!")
    return True

if __name__ == "__main__":
    success = verify_callback_fixes()
    if not success:
        print("\n⚠️ Some fixes may be incomplete. Please review the implementation.")
    exit(0 if success else 1)
